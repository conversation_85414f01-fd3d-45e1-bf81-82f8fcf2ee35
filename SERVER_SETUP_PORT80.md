# Server Setup Guide for Port 80 Deployment

This guide provides instructions for configuring your server at office.plansquare.co to run the HRMS application on port 80 (standard HTTP port).

## 🚨 Important Notes

- **Port 80 requires administrator/root privileges** to bind on most systems
- **Firewall configuration** may be needed to allow HTTP traffic
- **Web server conflicts** - ensure no other web server (Apache, Nginx, IIS) is using port 80

## 🐧 Linux Server Setup

### Option 1: Run with sudo (Simple but less secure)

```bash
# Navigate to application directory
cd /path/to/uploaded/hrms/files

# Run with sudo to bind to port 80
sudo dotnet HRMS.API.dll
```

### Option 2: Use authbind (Recommended for security)

```bash
# Install authbind
sudo apt-get install authbind  # Ubuntu/Debian
sudo yum install authbind      # CentOS/RHEL

# Allow user to bind to port 80
sudo touch /etc/authbind/byport/80
sudo chmod 500 /etc/authbind/byport/80
sudo chown yourusername /etc/authbind/byport/80

# Run application with authbind
authbind --deep dotnet HRMS.API.dll
```

### Option 3: Use systemd service with User=root

Create `/etc/systemd/system/hrms.service`:

```ini
[Unit]
Description=HRMS Application
After=network.target

[Service]
Type=notify
ExecStart=/usr/bin/dotnet /var/www/hrms/HRMS.API.dll
Restart=always
RestartSec=5
KillSignal=SIGINT
SyslogIdentifier=hrms
User=root
Group=root
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=ASPNETCORE_URLS=http://+:80
Environment=AllowedHosts=office.plansquare.co;localhost
WorkingDirectory=/var/www/hrms

[Install]
WantedBy=multi-user.target
```

Start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable hrms
sudo systemctl start hrms
sudo systemctl status hrms
```

### Option 4: Use capabilities (Most secure)

```bash
# Give the dotnet binary permission to bind to privileged ports
sudo setcap CAP_NET_BIND_SERVICE=+eip /usr/share/dotnet/dotnet

# Now you can run without sudo
dotnet HRMS.API.dll
```

## 🪟 Windows Server Setup

### Option 1: Run as Administrator

1. Open Command Prompt or PowerShell **as Administrator**
2. Navigate to the application directory
3. Run: `dotnet HRMS.API.dll`

### Option 2: Windows Service

1. Install the application as a Windows Service using `sc` command:

```cmd
sc create "HRMS Application" binPath="C:\path\to\dotnet.exe C:\path\to\HRMS.API.dll" start=auto
sc description "HRMS Application" "Human Resource Management System"
sc start "HRMS Application"
```

### Option 3: IIS Integration

1. Install IIS and ASP.NET Core Hosting Bundle
2. Create a new IIS site pointing to your application directory
3. Set the site binding to port 80
4. Configure `web.config` in the application root:

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\HRMS.API.dll" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_URLS" value="http://+:80" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>
```

## 🔥 Firewall Configuration

### Linux (UFW)
```bash
sudo ufw allow 80/tcp
sudo ufw reload
```

### Linux (firewalld)
```bash
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --reload
```

### Windows
```powershell
New-NetFirewallRule -DisplayName "HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
```

## 🔍 Troubleshooting

### Port 80 Already in Use

Check what's using port 80:

**Linux:**
```bash
sudo netstat -tlnp | grep :80
sudo lsof -i :80
```

**Windows:**
```cmd
netstat -ano | findstr :80
```

Stop conflicting services:
```bash
# Linux - Stop Apache/Nginx
sudo systemctl stop apache2
sudo systemctl stop nginx

# Windows - Stop IIS
iisreset /stop
```

### Permission Denied Errors

**Linux:**
- Ensure you're running with appropriate privileges (sudo, authbind, or capabilities)
- Check SELinux settings: `sudo setsebool -P httpd_can_network_connect 1`

**Windows:**
- Run Command Prompt/PowerShell as Administrator
- Check Windows Defender/Antivirus settings

### Application Not Responding

1. **Check if the process is running:**
   ```bash
   # Linux
   ps aux | grep dotnet
   
   # Windows
   tasklist | findstr dotnet
   ```

2. **Check application logs:**
   - Look for error messages in console output
   - Check system logs (journalctl on Linux, Event Viewer on Windows)

3. **Test local connectivity:**
   ```bash
   curl http://localhost/api/v1/health
   ```

4. **Check network binding:**
   ```bash
   # Linux
   sudo netstat -tlnp | grep :80
   
   # Windows
   netstat -ano | findstr :80
   ```

## 🔧 Performance Optimization

### For High Traffic

1. **Use a reverse proxy** (Nginx, Apache, or IIS) in front of the application
2. **Enable response compression** in the application
3. **Configure connection limits** and timeouts
4. **Use a load balancer** for multiple instances

### Example Nginx Configuration

```nginx
server {
    listen 80;
    server_name office.plansquare.co;
    
    location / {
        proxy_pass http://localhost:8080;  # Run app on different port
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## ✅ Verification Steps

After setup, verify the application is working:

1. **Health Check:** `curl http://office.plansquare.co/api/v1/health`
2. **Frontend:** Open `http://office.plansquare.co` in browser
3. **API Docs:** Visit `http://office.plansquare.co/api/swagger`
4. **Login Test:** Try logging in with default credentials

## 🔐 Security Considerations

1. **Use HTTPS in production** - Consider setting up SSL/TLS
2. **Regular updates** - Keep .NET runtime and OS updated
3. **Firewall rules** - Only allow necessary ports
4. **Access logs** - Monitor application access
5. **Backup strategy** - Regular backups of application and database

---

**Note:** Running on port 80 requires careful consideration of security and system administration. Consider using a reverse proxy setup for production environments.

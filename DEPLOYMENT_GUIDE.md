# HRMS Application Deployment Guide

This guide provides step-by-step instructions for deploying the HRMS application to the FTP server at office.plansquare.co.

## 📋 Prerequisites

### System Requirements
- **Docker Desktop** installed and running
- **PowerShell 5.1+** (Windows) or **PowerShell Core 6+** (Cross-platform)
- **Git** (for cloning/updating the repository)
- **Internet connection** for Docker image building and FTP upload

### Server Requirements
- **FTP Server**: office.plansquare.co
- **Web Server**: IIS, Apache, or Nginx configured to serve static files and proxy API requests
- **Port**: Application runs on port 5020
- **.NET 8 Runtime** (if running outside Docker)

## 🚀 Quick Deployment

### Option 1: PowerShell Script (Recommended)
```powershell
# Run the PowerShell deployment script
.\deploy-to-ftp.ps1

# With verbose output
.\deploy-to-ftp.ps1 -Verbose

# Skip Docker build (if image already exists)
.\deploy-to-ftp.ps1 -SkipBuild
```

### Option 2: <PERSON><PERSON> Script (Windows)
```cmd
# Run the batch deployment script
deploy-to-ftp.bat
```

## 📝 Manual Deployment Steps

If you prefer to deploy manually or need to troubleshoot, follow these steps:

### Step 1: Prepare the Environment
```bash
# Ensure you're in the project root directory
cd /path/to/hrms-plan

# Verify Docker is running
docker --version
```

### Step 2: Build the Docker Image
```bash
# Build the production Docker image
docker build -t plansquare-hrms .

# Verify the image was created
docker images | grep plansquare-hrms
```

### Step 3: Extract Application Files
```bash
# Create a temporary container
docker create --name hrms-temp-container plansquare-hrms

# Create deployment directory
mkdir deployment-files

# Copy files from container
docker cp hrms-temp-container:/app/. ./deployment-files/

# Cleanup the temporary container
docker rm hrms-temp-container
```

### Step 4: Upload to FTP Server
Use your preferred FTP client with these credentials:
- **Server**: office.plansquare.co
- **Username**: ftpOffPlan
- **Password**: &K78yaw01
- **Upload Path**: / (root directory)

Upload all files from the `deployment-files` directory to the FTP server.

## 🔧 Server Configuration

### Web Server Setup

#### Option 1: Direct .NET Application (Recommended)
If your server supports .NET 8 runtime:

1. **Upload Files**: Upload all files to your web server directory
2. **Set Permissions**: Ensure the application has read/write permissions
3. **Configure Port**: The application runs on port 5020
4. **Start Application**: 
   ```bash
   cd /path/to/uploaded/files
   dotnet HRMS.API.dll
   ```

#### Option 2: Reverse Proxy Setup
Configure your web server to proxy requests to the application:

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name office.plansquare.co;
    
    location / {
        proxy_pass http://localhost:5020;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

**IIS Configuration (web.config):**
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="Reverse Proxy" stopProcessing="true">
          <match url="(.*)" />
          <action type="Rewrite" url="http://localhost:5020/{R:1}" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

### Environment Variables
The application is pre-configured with production settings, but you can override them:

```bash
# Database connection
export ConnectionStrings__DefaultConnection="Server=**************,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;"

# Application URLs
export ASPNETCORE_URLS="http://+:5020"
export AllowedHosts="office.plansquare.co;localhost"

# JWT Configuration
export Jwt__SecretKey="your-super-secret-jwt-key-that-is-at-least-32-characters-long"
```

## 🔍 Verification

### 1. Health Check
After deployment, verify the application is running:
```bash
curl http://office.plansquare.co:5020/api/v1/health
```

Expected response:
```json
{
  "status": "Healthy",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 2. Frontend Access
Open your browser and navigate to:
- **Application**: http://office.plansquare.co:5020
- **API Documentation**: http://office.plansquare.co:5020/api/swagger

### 3. Login Test
Test the login functionality:
- **Super Admin**: <EMAIL> / Admin123!
- **Org Admin**: <EMAIL> / Admin123!

## 🛠️ Troubleshooting

### Common Issues

#### 1. Docker Build Fails
```bash
# Clear Docker cache and rebuild
docker system prune -f
docker build --no-cache -t plansquare-hrms .
```

#### 2. FTP Upload Fails
- Verify FTP credentials
- Check network connectivity
- Ensure FTP server allows passive mode
- Try uploading a small test file first

#### 3. Application Won't Start
- Check .NET 8 runtime is installed
- Verify database connectivity
- Check port 5020 is available
- Review application logs

#### 4. CORS Errors
The application is configured for office.plansquare.co. If accessing from a different domain:
- Update CORS settings in appsettings.Production.json
- Rebuild and redeploy

### Log Files
Application logs are written to:
- **Console Output**: When running directly
- **System Logs**: When running as a service
- **Custom Logs**: Check application directory for log files

## 📞 Support

If you encounter issues during deployment:

1. **Check Prerequisites**: Ensure all required software is installed
2. **Review Logs**: Check Docker build logs and application logs
3. **Test Connectivity**: Verify FTP and database connections
4. **Validate Configuration**: Ensure all settings are correct

## 🔄 Updates and Maintenance

### Updating the Application
1. Pull latest code changes
2. Run the deployment script again
3. The script will rebuild and redeploy automatically

### Database Migrations
The application automatically handles database migrations on startup. No manual intervention required.

### Backup Recommendations
- **Database**: Regular backups of SQL Server database
- **Application Files**: Keep a copy of deployment files
- **Configuration**: Backup any custom configuration files

---

**Note**: This deployment guide assumes you have the necessary permissions and access to the FTP server and target domain. Contact your system administrator if you need assistance with server configuration or permissions.

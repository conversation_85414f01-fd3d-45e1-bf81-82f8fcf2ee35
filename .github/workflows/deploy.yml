name: Deploy HRMS to FTP Server

on:
  workflow_dispatch:  # Manual trigger
  push:
    branches:
      - main
      - master
    paths-ignore:
      - '**.md'
      - '.gitignore'

env:
  FTP_SERVER: office.plansquare.co
  FTP_USERNAME: ftpOffPlan
  FTP_PASSWORD: "&K78yaw01"
  IMAGE_NAME: plansquare-hrms

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      run: |
        echo "🚀 Building HRMS Docker image..."
        docker build -t ${{ env.IMAGE_NAME }} .
        echo "✅ Docker image built successfully"
        
    - name: Extract application files from Docker image
      run: |
        echo "📦 Extracting application files from Docker container..."
        
        # Create temporary container
        docker create --name temp-container ${{ env.IMAGE_NAME }}
        
        # Create deployment directory
        mkdir -p deployment-files
        
        # Copy files from container
        docker cp temp-container:/app/. ./deployment-files/
        
        # Remove temporary container
        docker rm temp-container
        
        # List extracted files for verification
        echo "📁 Extracted files:"
        find deployment-files -type f | head -20
        echo "..."
        echo "Total files: $(find deployment-files -type f | wc -l)"
        
    - name: Install FTP client
      run: |
        sudo apt-get update
        sudo apt-get install -y lftp
        
    - name: Deploy to FTP server
      run: |
        echo "🌐 Deploying to FTP server: ${{ env.FTP_SERVER }}"
        
        # Create FTP script
        cat > ftp_deploy.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "Connecting to FTP server..."
        lftp -c "
          set ftp:ssl-allow no;
          set ftp:passive-mode on;
          set net:timeout 30;
          set net:max-retries 3;
          open ftp://$FTP_USERNAME:$FTP_PASSWORD@$FTP_SERVER;
          lcd deployment-files;
          cd /;
          echo 'Starting file upload...';
          mirror --reverse --delete --verbose --parallel=3 --exclude-glob='.git*' . .;
          echo 'Upload completed successfully!';
          quit
        "
        EOF
        
        chmod +x ftp_deploy.sh
        
        # Execute FTP deployment
        FTP_USERNAME="${{ env.FTP_USERNAME }}" \
        FTP_PASSWORD="${{ env.FTP_PASSWORD }}" \
        FTP_SERVER="${{ env.FTP_SERVER }}" \
        ./ftp_deploy.sh
        
        echo "✅ Deployment completed successfully!"
        echo "🌐 Application should be available at: http://${{ env.FTP_SERVER }}:5020"
        
    - name: Verify deployment
      run: |
        echo "🔍 Verifying deployment..."
        
        # Wait a moment for the server to process files
        sleep 10
        
        # Try to check if the server responds (this might fail if server isn't configured yet)
        if curl -f -s --connect-timeout 10 "http://${{ env.FTP_SERVER }}:5020/api/v1/health" > /dev/null 2>&1; then
          echo "✅ Health check passed - Application is responding"
        else
          echo "⚠️  Health check failed - This is expected if the server isn't configured to run the application yet"
          echo "📋 Next steps:"
          echo "   1. Ensure .NET 8 runtime is installed on the server"
          echo "   2. Configure the web server to serve the application on port 5020"
          echo "   3. Start the application: dotnet HRMS.API.dll"
        fi
        
    - name: Cleanup
      if: always()
      run: |
        echo "🧹 Cleaning up..."
        rm -rf deployment-files
        rm -f ftp_deploy.sh
        docker rmi ${{ env.IMAGE_NAME }} || true
        echo "✅ Cleanup completed"
        
    - name: Deployment Summary
      if: always()
      run: |
        echo "📊 Deployment Summary"
        echo "===================="
        echo "🏗️  Build Status: ${{ job.status }}"
        echo "🌐 Target Server: ${{ env.FTP_SERVER }}"
        echo "🔗 Application URL: http://${{ env.FTP_SERVER }}:5020"
        echo "📚 API Documentation: http://${{ env.FTP_SERVER }}:5020/api/swagger"
        echo "❤️  Health Check: http://${{ env.FTP_SERVER }}:5020/api/v1/health"
        echo ""
        echo "🔑 Default Login Credentials:"
        echo "   Super Admin: <EMAIL> / Admin123!"
        echo "   Org Admin: <EMAIL> / Admin123!"
        echo ""
        if [ "${{ job.status }}" = "success" ]; then
          echo "✅ Deployment completed successfully!"
        else
          echo "❌ Deployment failed. Check the logs above for details."
        fi

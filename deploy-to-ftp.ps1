# HRMS Application FTP Deployment Script
# This script builds the Docker image, extracts the application files, and uploads them to the FTP server

param(
    [string]$FtpServer = "office.plansquare.co",
    [string]$FtpUsername = "ftpOffPlan",
    [string]$FtpPassword = "&K78yaw01",
    [string]$RemotePath = "/",
    [string]$ImageName = "plansquare-hrms",
    [string]$ContainerName = "hrms-temp-container",
    [switch]$CleanupOnly,
    [switch]$SkipBuild,
    [switch]$Verbose
)

# Enable verbose output if requested
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "🚀 HRMS FTP Deployment Script" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# Function to log messages
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Function to cleanup Docker resources
function Cleanup-Docker {
    Write-Log "Cleaning up Docker resources..."
    
    # Stop and remove container if it exists
    $container = docker ps -a --filter "name=$ContainerName" --format "{{.Names}}"
    if ($container) {
        Write-Log "Stopping and removing container: $ContainerName"
        docker stop $ContainerName 2>$null
        docker rm $ContainerName 2>$null
    }
    
    # Remove image if it exists (optional - comment out to keep for faster rebuilds)
    # $image = docker images --filter "reference=$ImageName" --format "{{.Repository}}:{{.Tag}}"
    # if ($image) {
    #     Write-Log "Removing image: $ImageName"
    #     docker rmi $ImageName 2>$null
    # }
}

# Function to build Docker image
function Build-DockerImage {
    Write-Log "Building Docker image: $ImageName"
    
    # Build the Docker image
    $buildResult = docker build -t $ImageName . 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log "Docker build failed!" "ERROR"
        Write-Log $buildResult "ERROR"
        exit 1
    }
    
    Write-Log "Docker image built successfully" "SUCCESS"
}

# Function to extract files from Docker image
function Extract-Files {
    param([string]$OutputDir)
    
    Write-Log "Extracting application files from Docker image..."
    
    # Create temporary container
    $createResult = docker create --name $ContainerName $ImageName 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log "Failed to create container!" "ERROR"
        Write-Log $createResult "ERROR"
        exit 1
    }
    
    # Create output directory
    if (Test-Path $OutputDir) {
        Remove-Item $OutputDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    
    # Copy files from container
    Write-Log "Copying files from container to $OutputDir"
    $copyResult = docker cp "${ContainerName}:/app/." $OutputDir 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Log "Failed to copy files from container!" "ERROR"
        Write-Log $copyResult "ERROR"
        exit 1
    }
    
    Write-Log "Files extracted successfully" "SUCCESS"
}

# Function to upload files via FTP
function Upload-ToFTP {
    param([string]$LocalDir)
    
    Write-Log "Uploading files to FTP server: $FtpServer"
    
    try {
        # Create FTP request
        $ftpUri = "ftp://$FtpServer$RemotePath"
        Write-Log "FTP URI: $ftpUri"
        
        # Get all files recursively
        $files = Get-ChildItem -Path $LocalDir -Recurse -File
        $totalFiles = $files.Count
        $currentFile = 0
        
        Write-Log "Found $totalFiles files to upload"
        
        foreach ($file in $files) {
            $currentFile++
            $relativePath = $file.FullName.Substring($LocalDir.Length).Replace('\', '/')
            if ($relativePath.StartsWith('/')) {
                $relativePath = $relativePath.Substring(1)
            }
            
            $remoteFilePath = "$ftpUri$relativePath"
            Write-Progress -Activity "Uploading files" -Status "Uploading $relativePath" -PercentComplete (($currentFile / $totalFiles) * 100)
            
            # Create directory structure if needed
            $remoteDir = Split-Path $remoteFilePath -Parent
            if ($remoteDir -ne $ftpUri.TrimEnd('/')) {
                Create-FTPDirectory -FtpUri $remoteDir
            }
            
            # Upload file
            $ftpRequest = [System.Net.FtpWebRequest]::Create($remoteFilePath)
            $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::UploadFile
            $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($FtpUsername, $FtpPassword)
            $ftpRequest.UseBinary = $true
            $ftpRequest.UsePassive = $true
            
            $fileContent = [System.IO.File]::ReadAllBytes($file.FullName)
            $ftpRequest.ContentLength = $fileContent.Length
            
            $requestStream = $ftpRequest.GetRequestStream()
            $requestStream.Write($fileContent, 0, $fileContent.Length)
            $requestStream.Close()
            
            $response = $ftpRequest.GetResponse()
            $response.Close()
            
            if ($Verbose) {
                Write-Log "Uploaded: $relativePath"
            }
        }
        
        Write-Progress -Activity "Uploading files" -Completed
        Write-Log "All files uploaded successfully!" "SUCCESS"
        
    } catch {
        Write-Log "FTP upload failed: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Function to create FTP directory
function Create-FTPDirectory {
    param([string]$FtpUri)
    
    try {
        $ftpRequest = [System.Net.FtpWebRequest]::Create($FtpUri)
        $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::MakeDirectory
        $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($FtpUsername, $FtpPassword)
        $ftpRequest.UsePassive = $true
        
        $response = $ftpRequest.GetResponse()
        $response.Close()
    } catch {
        # Directory might already exist, ignore error
        if ($Verbose) {
            Write-Log "Directory creation result: $($_.Exception.Message)" "WARN"
        }
    }
}

# Main execution
try {
    # Cleanup only mode
    if ($CleanupOnly) {
        Cleanup-Docker
        Write-Log "Cleanup completed" "SUCCESS"
        exit 0
    }
    
    # Check if Docker is available
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Log "Docker is not installed or not available in PATH!" "ERROR"
        exit 1
    }
    Write-Log "Docker version: $dockerVersion"
    
    # Cleanup previous resources
    Cleanup-Docker
    
    # Build Docker image (unless skipped)
    if (-not $SkipBuild) {
        Build-DockerImage
    } else {
        Write-Log "Skipping Docker build as requested"
    }
    
    # Extract files
    $outputDir = Join-Path $PSScriptRoot "deployment-files"
    Extract-Files -OutputDir $outputDir
    
    # Upload to FTP
    Upload-ToFTP -LocalDir $outputDir
    
    # Cleanup
    Cleanup-Docker
    
    # Remove local deployment files (optional)
    Write-Log "Cleaning up local deployment files..."
    Remove-Item $outputDir -Recurse -Force -ErrorAction SilentlyContinue
    
    Write-Log "Deployment completed successfully!" "SUCCESS"
    Write-Host ""
    Write-Host "🎉 Your HRMS application has been deployed to office.plansquare.co" -ForegroundColor Green
    Write-Host "   You can now access it at: http://office.plansquare.co:5020" -ForegroundColor Cyan
    Write-Host ""
    
} catch {
    Write-Log "Deployment failed: $($_.Exception.Message)" "ERROR"
    Cleanup-Docker
    exit 1
}

# HRMS Deployment Checklist

Use this checklist to ensure a successful deployment of the HRMS application to office.plansquare.co.

## ✅ Pre-Deployment Checklist

### Local Environment
- [ ] Docker Desktop is installed and running
- [ ] PowerShell 5.1+ or PowerShell Core 6+ is available
- [ ] Git repository is up to date with latest changes
- [ ] All recent changes have been tested locally
- [ ] Database connection string is correct in configuration

### Server Environment
- [ ] FTP server (office.plansquare.co) is accessible
- [ ] FTP credentials are correct: ftpOffPlan / &K78yaw01
- [ ] Target server has .NET 8 Runtime installed (if running directly)
- [ ] Port 5020 is available and not blocked by firewall
- [ ] SQL Server database (**************:2829) is accessible from server

### Configuration Review
- [ ] CORS policy includes office.plansquare.co domain
- [ ] AllowedHosts is configured for office.plansquare.co
- [ ] Production environment variables are set correctly
- [ ] JWT secret key is configured for production
- [ ] Database connection string is correct for production

## 🚀 Deployment Steps

### Step 1: Build and Deploy
- [ ] Run deployment script: `.\deploy-to-ftp.ps1` or `deploy-to-ftp.bat`
- [ ] Verify Docker image builds successfully
- [ ] Confirm files are extracted from Docker container
- [ ] Verify FTP upload completes without errors
- [ ] Check that all files are uploaded to FTP server

### Step 2: Server Configuration
- [ ] Configure web server (IIS, Nginx, or direct .NET hosting)
- [ ] Set up reverse proxy if using standard HTTP ports
- [ ] Configure SSL certificate (recommended)
- [ ] Set appropriate firewall rules
- [ ] Configure systemd service (Linux) or Windows Service

### Step 3: Application Startup
- [ ] Start the HRMS application service
- [ ] Verify application is listening on port 5020
- [ ] Check application logs for any startup errors
- [ ] Confirm database connection is established
- [ ] Verify auto-provisioning is working (if enabled)

## 🔍 Post-Deployment Verification

### Health Checks
- [ ] Health endpoint responds: `http://office.plansquare.co:5020/api/v1/health`
- [ ] Frontend loads: `http://office.plansquare.co:5020`
- [ ] API documentation accessible: `http://office.plansquare.co:5020/api/swagger`
- [ ] Static assets (CSS, JS, images) load correctly
- [ ] No console errors in browser developer tools

### Authentication Testing
- [ ] Super Admin login works: <EMAIL> / Admin123!
- [ ] Organization Admin login works: <EMAIL> / Admin123!
- [ ] JWT tokens are generated correctly
- [ ] Session management works properly
- [ ] Logout functionality works

### Core Functionality Testing
- [ ] Dashboard loads with correct data
- [ ] Employee management functions work
- [ ] Attendance system is operational
- [ ] Leave management is functional
- [ ] Task management works correctly
- [ ] Organization admin features are accessible

### Database Connectivity
- [ ] Database connection is stable
- [ ] Multi-tenant schema isolation works
- [ ] CRUD operations function correctly
- [ ] Data persistence is working
- [ ] Database migrations applied successfully

## 🛠️ Troubleshooting Checklist

### If Application Won't Start
- [ ] Check .NET 8 Runtime is installed
- [ ] Verify port 5020 is not in use by another application
- [ ] Check firewall settings allow traffic on port 5020
- [ ] Review application logs for error messages
- [ ] Verify database connection string is correct
- [ ] Check file permissions on application directory

### If FTP Upload Fails
- [ ] Verify FTP server is accessible
- [ ] Check FTP credentials are correct
- [ ] Ensure network connectivity to FTP server
- [ ] Try uploading a small test file manually
- [ ] Check if passive FTP mode is required
- [ ] Verify sufficient disk space on FTP server

### If Frontend Doesn't Load
- [ ] Check if static files were uploaded correctly
- [ ] Verify web server is serving static files
- [ ] Check browser console for JavaScript errors
- [ ] Ensure CORS policy allows the domain
- [ ] Verify API endpoints are accessible
- [ ] Check if reverse proxy is configured correctly

### If API Calls Fail
- [ ] Verify API endpoints are accessible
- [ ] Check CORS configuration includes the domain
- [ ] Ensure JWT authentication is working
- [ ] Verify database connectivity
- [ ] Check API logs for error messages
- [ ] Test API endpoints directly with curl or Postman

## 📊 Performance Verification

### Load Testing
- [ ] Test with multiple concurrent users
- [ ] Verify response times are acceptable
- [ ] Check memory usage under load
- [ ] Monitor database connection pool
- [ ] Test file upload functionality

### Security Testing
- [ ] Verify HTTPS is working (if configured)
- [ ] Test authentication and authorization
- [ ] Check for proper error handling
- [ ] Verify sensitive data is not exposed in logs
- [ ] Test mobile device blocking (if enabled)

## 📝 Documentation Updates

### Post-Deployment
- [ ] Update deployment documentation with any changes
- [ ] Document any server-specific configurations
- [ ] Record any troubleshooting steps taken
- [ ] Update monitoring and alerting configurations
- [ ] Create backup and recovery procedures

### User Communication
- [ ] Notify users of the new deployment
- [ ] Provide updated access URLs
- [ ] Share any new login credentials
- [ ] Document any feature changes or updates
- [ ] Provide support contact information

## 🔄 Ongoing Maintenance

### Regular Tasks
- [ ] Set up automated backups
- [ ] Configure log rotation
- [ ] Set up monitoring and alerting
- [ ] Schedule regular security updates
- [ ] Plan for SSL certificate renewal

### Monitoring Setup
- [ ] Application health monitoring
- [ ] Database performance monitoring
- [ ] Server resource monitoring
- [ ] Log aggregation and analysis
- [ ] User activity monitoring

## 📞 Emergency Contacts

### Technical Support
- **System Administrator**: [Contact Information]
- **Database Administrator**: [Contact Information]
- **Network Administrator**: [Contact Information]
- **Application Developer**: [Contact Information]

### Rollback Plan
- [ ] Document rollback procedures
- [ ] Keep previous version backup
- [ ] Test rollback process
- [ ] Identify rollback triggers
- [ ] Communicate rollback procedures to team

---

**Note**: Complete each checklist item before proceeding to the next section. If any item fails, address the issue before continuing with the deployment process.

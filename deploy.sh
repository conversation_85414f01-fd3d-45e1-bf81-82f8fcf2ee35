#!/bin/bash

# HRMS Application FTP Deployment Script
# This script builds the Docker image, extracts files, and uploads to FTP server

set -e  # Exit on any error

# Configuration
FTP_SERVER="office.plansquare.co"
FTP_USERNAME="ftpOffPlan"
FTP_PASSWORD="&K78yaw01"
FTP_REMOTE_PATH="/"
IMAGE_NAME="plansquare-hrms"
CONTAINER_NAME="hrms-temp-container"
DEPLOYMENT_DIR="deployment-files"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup resources
cleanup() {
    print_status "Cleaning up resources..."
    
    # Remove container if it exists
    if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker rm -f ${CONTAINER_NAME} >/dev/null 2>&1
    fi
    
    # Remove deployment directory
    if [ -d "${DEPLOYMENT_DIR}" ]; then
        rm -rf ${DEPLOYMENT_DIR}
    fi
    
    print_success "Cleanup completed"
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Main deployment function
main() {
    echo ""
    echo "🚀 HRMS Application FTP Deployment"
    echo "=================================="
    echo ""
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not available in PATH!"
        exit 1
    fi
    
    # Check if lftp is available
    if ! command -v lftp &> /dev/null; then
        print_error "lftp is not installed. Please install it:"
        echo "  Ubuntu/Debian: sudo apt-get install lftp"
        echo "  macOS: brew install lftp"
        echo "  CentOS/RHEL: sudo yum install lftp"
        exit 1
    fi
    
    print_status "Docker version: $(docker --version)"
    print_status "lftp version: $(lftp --version | head -1)"
    echo ""
    
    # Step 1: Build Docker image
    print_status "Building Docker image: ${IMAGE_NAME}"
    if docker build -t ${IMAGE_NAME} .; then
        print_success "Docker image built successfully"
    else
        print_error "Docker build failed!"
        exit 1
    fi
    echo ""
    
    # Step 2: Extract files from Docker image
    print_status "Extracting application files from Docker image..."
    
    # Create temporary container
    if docker create --name ${CONTAINER_NAME} ${IMAGE_NAME}; then
        print_success "Temporary container created"
    else
        print_error "Failed to create temporary container!"
        exit 1
    fi
    
    # Create deployment directory
    mkdir -p ${DEPLOYMENT_DIR}
    
    # Copy files from container
    if docker cp ${CONTAINER_NAME}:/app/. ./${DEPLOYMENT_DIR}/; then
        print_success "Files extracted successfully"
        
        # Show file count
        file_count=$(find ${DEPLOYMENT_DIR} -type f | wc -l)
        print_status "Total files extracted: ${file_count}"
    else
        print_error "Failed to extract files from container!"
        exit 1
    fi
    echo ""
    
    # Step 3: Upload to FTP server
    print_status "Uploading files to FTP server: ${FTP_SERVER}"
    
    # Create FTP script
    cat > ftp_upload.lftp << EOF
set ftp:ssl-allow no
set ftp:passive-mode on
set net:timeout 30
set net:max-retries 3
open ftp://${FTP_USERNAME}:${FTP_PASSWORD}@${FTP_SERVER}
lcd ${DEPLOYMENT_DIR}
cd ${FTP_REMOTE_PATH}
echo "Starting file upload..."
mirror --reverse --delete --verbose --parallel=3 --exclude-glob='.git*' . .
echo "Upload completed!"
quit
EOF
    
    # Execute FTP upload
    if lftp -f ftp_upload.lftp; then
        print_success "FTP upload completed successfully!"
        rm -f ftp_upload.lftp
    else
        print_error "FTP upload failed!"
        rm -f ftp_upload.lftp
        exit 1
    fi
    echo ""
    
    # Step 4: Deployment summary
    echo "🎉 Deployment Summary"
    echo "===================="
    print_success "Application deployed to: http://${FTP_SERVER}:5020"
    echo ""
    echo "📚 Available URLs:"
    echo "   • Main Application: http://${FTP_SERVER}:5020"
    echo "   • API Documentation: http://${FTP_SERVER}:5020/api/swagger"
    echo "   • Health Check: http://${FTP_SERVER}:5020/api/v1/health"
    echo ""
    echo "🔑 Default Login Credentials:"
    echo "   • Super Admin: <EMAIL> / Admin123!"
    echo "   • Org Admin: <EMAIL> / Admin123!"
    echo ""
    echo "📋 Next Steps:"
    echo "   1. Ensure .NET 8 runtime is installed on the server"
    echo "   2. Configure the web server to serve the application on port 5020"
    echo "   3. Start the application: dotnet HRMS.API.dll"
    echo ""
    
    # Optional: Test connectivity
    print_status "Testing server connectivity..."
    if curl -f -s --connect-timeout 10 "http://${FTP_SERVER}:5020/api/v1/health" > /dev/null 2>&1; then
        print_success "✅ Server is responding - Application is running!"
    else
        print_warning "⚠️  Server is not responding yet - This is normal if the server needs configuration"
    fi
    
    echo ""
    print_success "Deployment process completed!"
}

# Run main function
main "$@"

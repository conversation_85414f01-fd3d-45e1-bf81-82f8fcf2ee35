version: '3.8'

services:
  # Build the HRMS application
  hrms-build:
    build:
      context: .
      dockerfile: Dockerfile
    image: plansquare-hrms:latest
    container_name: hrms-build-container
    # This service is just for building, it will exit immediately
    command: echo "Build completed"
    
  # FTP deployment service
  hrms-deploy:
    image: alpine:latest
    container_name: hrms-deploy-container
    depends_on:
      - hrms-build
    environment:
      - FTP_SERVER=office.plansquare.co
      - FTP_USERNAME=ftpOffPlan
      - FTP_PASSWORD=&K78yaw01
      - FTP_REMOTE_PATH=/
    volumes:
      - deployment-files:/deployment-files
      - ./deploy-script.sh:/deploy-script.sh:ro
    command: |
      sh -c "
        echo '🚀 Starting HRMS FTP Deployment Process...'
        echo '============================================'
        
        # Install required packages
        apk add --no-cache lftp docker-cli
        
        # Extract files from the built image
        echo '📦 Extracting application files from Docker image...'
        docker create --name temp-extract plansquare-hrms:latest
        docker cp temp-extract:/app/. /deployment-files/
        docker rm temp-extract
        
        # List extracted files
        echo '📁 Files to deploy:'
        find /deployment-files -type f | head -10
        echo '...'
        echo 'Total files:' $$(find /deployment-files -type f | wc -l)
        
        # Deploy to FTP
        echo '🌐 Deploying to FTP server: $$FTP_SERVER'
        lftp -c \"
          set ftp:ssl-allow no;
          set ftp:passive-mode on;
          set net:timeout 30;
          set net:max-retries 3;
          open ftp://$$FTP_USERNAME:$$FTP_PASSWORD@$$FTP_SERVER;
          lcd /deployment-files;
          cd $$FTP_REMOTE_PATH;
          echo 'Starting file upload...';
          mirror --reverse --delete --verbose --parallel=3 --exclude-glob='.git*' . .;
          echo 'Upload completed!';
          quit
        \"
        
        if [ $$? -eq 0 ]; then
          echo '✅ FTP deployment completed successfully!'
          echo '🌐 Application deployed to: http://$$FTP_SERVER'
          echo ''
          echo '📋 Next Steps:'
          echo '   1. Ensure .NET 8 runtime is installed on the server'
          echo '   2. Configure web server to serve the application on port 80'
          echo '   3. Start the application: dotnet HRMS.API.dll'
          echo '   4. Ensure the server has permissions to bind to port 80 (may require sudo/admin)'
          echo ''
          echo '🔑 Default Login Credentials:'
          echo '   Super Admin: <EMAIL> / Admin123!'
          echo '   Org Admin: <EMAIL> / Admin123!'
        else
          echo '❌ FTP deployment failed!'
          exit 1
        fi
      "
    # Mount Docker socket to allow container to interact with Docker
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - deployment-files:/deployment-files

volumes:
  deployment-files:
    driver: local

#!/bin/bash

# Simple HRMS FTP Deployment Script
# This script uses curl for FTP upload to handle special characters better

set -e

# Configuration
FTP_SERVER="office.plansquare.co"
FTP_USERNAME="ftpOffPlan"
FTP_PASSWORD="&K78yaw01"
IMAGE_NAME="plansquare-hrms"
CONTAINER_NAME="hrms-temp-container"
DEPLOYMENT_DIR="deployment-files"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    docker rm -f ${CONTAINER_NAME} >/dev/null 2>&1 || true
    rm -rf ${DEPLOYMENT_DIR} || true
    rm -f file_list.txt || true
}

trap cleanup EXIT

echo ""
echo "🚀 HRMS Simple FTP Deployment"
echo "============================="
echo ""

# Check prerequisites
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed!"
    exit 1
fi

if ! command -v curl &> /dev/null; then
    print_error "curl is not installed!"
    exit 1
fi

# Step 1: Build Docker image
print_status "Building Docker image..."
if docker build -t ${IMAGE_NAME} .; then
    print_success "Docker image built successfully"
else
    print_error "Docker build failed!"
    exit 1
fi

# Step 2: Extract files
print_status "Extracting files from Docker image..."
docker create --name ${CONTAINER_NAME} ${IMAGE_NAME}
mkdir -p ${DEPLOYMENT_DIR}
docker cp ${CONTAINER_NAME}:/app/. ./${DEPLOYMENT_DIR}/

file_count=$(find ${DEPLOYMENT_DIR} -type f | wc -l)
print_success "Extracted ${file_count} files"

# Step 3: Test FTP connection
print_status "Testing FTP connection..."
if curl -s --connect-timeout 10 --user "${FTP_USERNAME}:${FTP_PASSWORD}" "ftp://${FTP_SERVER}/" > /dev/null; then
    print_success "FTP connection successful"
else
    print_error "FTP connection failed!"
    print_error "Please verify:"
    print_error "  - Server: ${FTP_SERVER}"
    print_error "  - Username: ${FTP_USERNAME}"
    print_error "  - Password: [hidden]"
    exit 1
fi

# Step 4: Upload files using curl
print_status "Uploading files to FTP server..."

# Create list of files to upload
find ${DEPLOYMENT_DIR} -type f > file_list.txt
total_files=$(wc -l < file_list.txt)
current_file=0

print_status "Uploading ${total_files} files..."

while IFS= read -r file_path; do
    current_file=$((current_file + 1))
    
    # Get relative path
    relative_path=${file_path#${DEPLOYMENT_DIR}/}
    
    # Create remote directory if needed
    remote_dir=$(dirname "${relative_path}")
    if [ "${remote_dir}" != "." ]; then
        curl -s --ftp-create-dirs --user "${FTP_USERNAME}:${FTP_PASSWORD}" \
             "ftp://${FTP_SERVER}/${remote_dir}/" > /dev/null 2>&1 || true
    fi
    
    # Upload file
    if curl -s --upload-file "${file_path}" --user "${FTP_USERNAME}:${FTP_PASSWORD}" \
            "ftp://${FTP_SERVER}/${relative_path}"; then
        if [ $((current_file % 10)) -eq 0 ] || [ ${current_file} -eq ${total_files} ]; then
            print_status "Uploaded ${current_file}/${total_files} files"
        fi
    else
        print_error "Failed to upload: ${relative_path}"
    fi
    
done < file_list.txt

print_success "All files uploaded successfully!"

# Step 5: Verify upload
print_status "Verifying upload..."
if curl -s --user "${FTP_USERNAME}:${FTP_PASSWORD}" "ftp://${FTP_SERVER}/" | grep -q "HRMS.API.dll"; then
    print_success "Upload verification successful - HRMS.API.dll found on server"
else
    print_error "Upload verification failed - HRMS.API.dll not found on server"
fi

# Step 6: Summary
echo ""
echo "🎉 Deployment Summary"
echo "===================="
print_success "Application deployed to: http://${FTP_SERVER}"
echo ""
echo "📚 Available URLs:"
echo "   • Main Application: http://${FTP_SERVER}"
echo "   • API Documentation: http://${FTP_SERVER}/api/swagger"
echo "   • Health Check: http://${FTP_SERVER}/api/v1/health"
echo ""
echo "🔑 Default Login Credentials:"
echo "   • Super Admin: <EMAIL> / Admin123!"
echo "   • Org Admin: <EMAIL> / Admin123!"
echo ""
echo "📋 Next Steps:"
echo "   1. SSH into your server: office.plansquare.co"
echo "   2. Navigate to the uploaded files directory"
echo "   3. Run: sudo dotnet HRMS.API.dll"
echo "   4. Access the application at: http://office.plansquare.co"
echo ""

# Test connectivity
print_status "Testing server connectivity..."
if curl -f -s --connect-timeout 10 "http://${FTP_SERVER}/api/v1/health" > /dev/null 2>&1; then
    print_success "✅ Server is responding - Application is running!"
else
    print_status "⚠️  Server is not responding yet - This is normal if the server needs to be started"
fi

print_success "Deployment completed!"

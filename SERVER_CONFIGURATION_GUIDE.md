# Server Configuration Guide for HRMS Application

This guide provides detailed instructions for configuring the web server at office.plansquare.co to properly serve the HRMS application.

## 🏗️ Architecture Overview

The HRMS application uses a **single-port architecture** where:
- **Frontend**: React SPA served as static files
- **Backend**: .NET Core API serving both static files and API endpoints
- **Port**: Application runs on port 5020
- **Database**: External SQL Server (**************:2829)

## 🖥️ Server Configuration Options

### Option 1: Direct .NET Application Hosting (Recommended)

This is the simplest approach where the .NET application serves everything directly.

#### Prerequisites
- **.NET 8 Runtime** installed on the server
- **Port 5020** available and accessible
- **Firewall** configured to allow traffic on port 5020

#### Setup Steps

1. **Install .NET 8 Runtime**
   ```bash
   # Ubuntu/Debian
   wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
   sudo dpkg -i packages-microsoft-prod.deb
   sudo apt-get update
   sudo apt-get install -y aspnetcore-runtime-8.0
   
   # CentOS/RHEL
   sudo dnf install aspnetcore-runtime-8.0
   
   # Windows Server
   # Download and install from: https://dotnet.microsoft.com/download/dotnet/8.0
   ```

2. **Create Application Directory**
   ```bash
   sudo mkdir -p /var/www/hrms
   sudo chown -R www-data:www-data /var/www/hrms  # Linux
   # or
   # Create C:\inetpub\hrms on Windows
   ```

3. **Upload Application Files**
   - Use the deployment scripts to upload files to `/var/www/hrms` (Linux) or `C:\inetpub\hrms` (Windows)

4. **Create Systemd Service (Linux)**
   ```bash
   sudo nano /etc/systemd/system/hrms.service
   ```
   
   Add the following content:
   ```ini
   [Unit]
   Description=HRMS Application
   After=network.target
   
   [Service]
   Type=notify
   ExecStart=/usr/bin/dotnet /var/www/hrms/HRMS.API.dll
   Restart=always
   RestartSec=5
   KillSignal=SIGINT
   SyslogIdentifier=hrms
   User=www-data
   Environment=ASPNETCORE_ENVIRONMENT=Production
   Environment=ASPNETCORE_URLS=http://+:5020
   Environment=AllowedHosts=office.plansquare.co;localhost
   WorkingDirectory=/var/www/hrms
   
   [Install]
   WantedBy=multi-user.target
   ```

5. **Start the Service**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable hrms
   sudo systemctl start hrms
   sudo systemctl status hrms
   ```

6. **Configure Firewall**
   ```bash
   # Ubuntu/Debian (UFW)
   sudo ufw allow 5020/tcp
   
   # CentOS/RHEL (firewalld)
   sudo firewall-cmd --permanent --add-port=5020/tcp
   sudo firewall-cmd --reload
   ```

### Option 2: Reverse Proxy with Nginx

Use this option if you want to serve the application on standard HTTP port (80) or HTTPS port (443).

#### Nginx Configuration

1. **Install Nginx**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install nginx
   
   # CentOS/RHEL
   sudo dnf install nginx
   ```

2. **Create Nginx Configuration**
   ```bash
   sudo nano /etc/nginx/sites-available/hrms
   ```
   
   Add the following configuration:
   ```nginx
   server {
       listen 80;
       server_name office.plansquare.co;
       
       # Security headers
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header Referrer-Policy "no-referrer-when-downgrade" always;
       add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
       
       # Proxy all requests to the .NET application
       location / {
           proxy_pass http://localhost:5020;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection keep-alive;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
           proxy_read_timeout 300s;
           proxy_connect_timeout 75s;
       }
       
       # Optional: Serve static files directly (for better performance)
       location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
           proxy_pass http://localhost:5020;
           expires 1y;
           add_header Cache-Control "public, immutable";
       }
       
       # Health check endpoint
       location /health {
           proxy_pass http://localhost:5020/api/v1/health;
           access_log off;
       }
   }
   ```

3. **Enable the Site**
   ```bash
   sudo ln -s /etc/nginx/sites-available/hrms /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

#### SSL/HTTPS Configuration (Optional but Recommended)

1. **Install Certbot**
   ```bash
   sudo apt install certbot python3-certbot-nginx
   ```

2. **Obtain SSL Certificate**
   ```bash
   sudo certbot --nginx -d office.plansquare.co
   ```

### Option 3: IIS with ASP.NET Core Module (Windows Server)

1. **Install IIS and ASP.NET Core Module**
   - Enable IIS through Windows Features
   - Download and install ASP.NET Core Hosting Bundle

2. **Create IIS Site**
   - Open IIS Manager
   - Create new site: "HRMS"
   - Physical path: `C:\inetpub\hrms`
   - Binding: office.plansquare.co on port 80

3. **Configure web.config**
   Create `web.config` in the application root:
   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <configuration>
     <location path="." inheritInChildApplications="false">
       <system.webServer>
         <handlers>
           <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
         </handlers>
         <aspNetCore processPath="dotnet" 
                     arguments=".\HRMS.API.dll" 
                     stdoutLogEnabled="false" 
                     stdoutLogFile=".\logs\stdout" 
                     hostingModel="inprocess" />
       </system.webServer>
     </location>
   </configuration>
   ```

## 🔧 Configuration Details

### Environment Variables

Set these environment variables for production:

```bash
# Application Configuration
ASPNETCORE_ENVIRONMENT=Production
ASPNETCORE_URLS=http://+:5020
AllowedHosts=office.plansquare.co;localhost

# Database Configuration
ConnectionStrings__DefaultConnection="Server=**************,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;"

# JWT Configuration
Jwt__SecretKey="your-super-secret-jwt-key-that-is-at-least-32-characters-long"
Jwt__Issuer="HRMS.API"
Jwt__Audience="HRMS.Client"
Jwt__ExpirationMinutes="480"

# Database Settings
Database__SchemaPrefix="org_"
Database__AutoProvision="true"
Database__TimeoutSeconds="300"

# Security Settings
Security__EnableMobileBlocking="true"
```

### Firewall Configuration

#### Linux (UFW)
```bash
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 5020/tcp  # If using direct hosting
sudo ufw enable
```

#### Linux (firewalld)
```bash
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=5020/tcp  # If using direct hosting
sudo firewall-cmd --reload
```

#### Windows Server
```powershell
# Allow inbound traffic on port 5020
New-NetFirewallRule -DisplayName "HRMS Application" -Direction Inbound -Protocol TCP -LocalPort 5020 -Action Allow

# Allow HTTP and HTTPS
New-NetFirewallRule -DisplayName "HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
New-NetFirewallRule -DisplayName "HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
```

## 🔍 Monitoring and Logging

### Log Locations

- **Application Logs**: Check the application directory for log files
- **System Logs**: 
  - Linux: `/var/log/syslog` or `journalctl -u hrms`
  - Windows: Event Viewer > Application Logs

### Health Monitoring

Set up monitoring for:
- **Health Endpoint**: `http://office.plansquare.co:5020/api/v1/health`
- **Application Process**: Monitor the .NET process
- **Database Connectivity**: Monitor SQL Server connection
- **Disk Space**: Ensure adequate disk space for logs and uploads

### Performance Tuning

1. **Memory Settings**
   ```bash
   # Set memory limits for the application
   export DOTNET_GCHeapHardLimit=**********  # 1GB limit
   ```

2. **Connection Pooling**
   - The application is configured with connection pooling
   - Monitor database connections under load

3. **Static File Caching**
   - Configure appropriate cache headers for static assets
   - Consider using a CDN for better performance

## 🚨 Security Considerations

1. **HTTPS**: Always use HTTPS in production
2. **Firewall**: Restrict access to necessary ports only
3. **Updates**: Keep the server and .NET runtime updated
4. **Database**: Ensure SQL Server is properly secured
5. **Backups**: Implement regular backup procedures

## 🔄 Maintenance Tasks

### Regular Tasks
- **Monitor Logs**: Check for errors and warnings
- **Update Application**: Deploy new versions as needed
- **Database Maintenance**: Regular backups and maintenance
- **Certificate Renewal**: Renew SSL certificates before expiry

### Troubleshooting Commands
```bash
# Check application status
sudo systemctl status hrms

# View application logs
sudo journalctl -u hrms -f

# Test application health
curl http://localhost:5020/api/v1/health

# Check port availability
netstat -tlnp | grep :5020
```

---

**Note**: This guide assumes you have administrative access to the server. Adjust paths and commands based on your specific server configuration and operating system.

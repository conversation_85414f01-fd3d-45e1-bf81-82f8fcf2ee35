@echo off
REM HRMS Application FTP Deployment Script (Batch Version)
REM This script builds the Docker image, extracts files, and uploads to FTP server

setlocal enabledelayedexpansion

REM Configuration
set FTP_SERVER=office.plansquare.co
set FTP_USERNAME=ftpOffPlan
set FTP_PASSWORD=^&K78yaw01
set IMAGE_NAME=plansquare-hrms
set CONTAINER_NAME=hrms-temp-container
set OUTPUT_DIR=%~dp0deployment-files

echo.
echo ================================
echo 🚀 HRMS FTP Deployment Script
echo ================================
echo.

REM Check if Docker is available
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not available in PATH!
    pause
    exit /b 1
)

echo [INFO] Docker is available
echo.

REM Cleanup previous resources
echo [INFO] Cleaning up previous Docker resources...
docker stop %CONTAINER_NAME% >nul 2>&1
docker rm %CONTAINER_NAME% >nul 2>&1

REM Build Docker image
echo [INFO] Building Docker image: %IMAGE_NAME%
docker build -t %IMAGE_NAME% .
if errorlevel 1 (
    echo [ERROR] Docker build failed!
    pause
    exit /b 1
)
echo [SUCCESS] Docker image built successfully
echo.

REM Create temporary container
echo [INFO] Creating temporary container...
docker create --name %CONTAINER_NAME% %IMAGE_NAME%
if errorlevel 1 (
    echo [ERROR] Failed to create container!
    pause
    exit /b 1
)

REM Create output directory
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"
mkdir "%OUTPUT_DIR%"

REM Extract files from container
echo [INFO] Extracting application files...
docker cp %CONTAINER_NAME%:/app/. "%OUTPUT_DIR%"
if errorlevel 1 (
    echo [ERROR] Failed to copy files from container!
    docker rm %CONTAINER_NAME% >nul 2>&1
    pause
    exit /b 1
)
echo [SUCCESS] Files extracted successfully
echo.

REM Upload files using PowerShell FTP (fallback method)
echo [INFO] Uploading files to FTP server...
powershell -Command "& {
    $ftpServer = '%FTP_SERVER%'
    $ftpUsername = '%FTP_USERNAME%'
    $ftpPassword = '%FTP_PASSWORD%'
    $localDir = '%OUTPUT_DIR%'
    
    Write-Host 'Connecting to FTP server: ' $ftpServer
    
    try {
        $files = Get-ChildItem -Path $localDir -Recurse -File
        $totalFiles = $files.Count
        $currentFile = 0
        
        Write-Host 'Found' $totalFiles 'files to upload'
        
        foreach ($file in $files) {
            $currentFile++
            $relativePath = $file.FullName.Substring($localDir.Length).Replace('\', '/')
            if ($relativePath.StartsWith('/')) {
                $relativePath = $relativePath.Substring(1)
            }
            
            $ftpUri = 'ftp://' + $ftpServer + '/' + $relativePath
            Write-Progress -Activity 'Uploading files' -Status ('Uploading ' + $relativePath) -PercentComplete (($currentFile / $totalFiles) * 100)
            
            try {
                $ftpRequest = [System.Net.FtpWebRequest]::Create($ftpUri)
                $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::UploadFile
                $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($ftpUsername, $ftpPassword)
                $ftpRequest.UseBinary = $true
                $ftpRequest.UsePassive = $true
                
                $fileContent = [System.IO.File]::ReadAllBytes($file.FullName)
                $ftpRequest.ContentLength = $fileContent.Length
                
                $requestStream = $ftpRequest.GetRequestStream()
                $requestStream.Write($fileContent, 0, $fileContent.Length)
                $requestStream.Close()
                
                $response = $ftpRequest.GetResponse()
                $response.Close()
                
            } catch {
                Write-Host 'Failed to upload:' $relativePath '- Error:' $_.Exception.Message -ForegroundColor Red
            }
        }
        
        Write-Progress -Activity 'Uploading files' -Completed
        Write-Host 'Upload completed!' -ForegroundColor Green
        
    } catch {
        Write-Host 'FTP upload failed:' $_.Exception.Message -ForegroundColor Red
        exit 1
    }
}"

if errorlevel 1 (
    echo [ERROR] FTP upload failed!
    goto cleanup
)

echo [SUCCESS] Files uploaded successfully!
echo.

:cleanup
REM Cleanup Docker resources
echo [INFO] Cleaning up Docker resources...
docker stop %CONTAINER_NAME% >nul 2>&1
docker rm %CONTAINER_NAME% >nul 2>&1

REM Remove local deployment files
echo [INFO] Cleaning up local deployment files...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo.
echo ================================
echo 🎉 Deployment completed!
echo ================================
echo.
echo Your HRMS application has been deployed to office.plansquare.co
echo You can now access it at: http://office.plansquare.co:5020
echo.
echo Note: Make sure your web server is configured to serve the application
echo on port 5020 or configure a reverse proxy to forward requests.
echo.

pause

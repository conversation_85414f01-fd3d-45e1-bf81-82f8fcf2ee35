@echo off
REM HRMS Application FTP Deployment Script (Windows)
REM This script builds the Docker image, extracts files, and uploads to FTP server

setlocal enabledelayedexpansion

REM Configuration
set FTP_SERVER=office.plansquare.co
set FTP_USERNAME=ftpOffPlan
set FTP_PASSWORD=^&K78yaw01
set FTP_REMOTE_PATH=/
set IMAGE_NAME=plansquare-hrms
set CONTAINER_NAME=hrms-temp-container
set DEPLOYMENT_DIR=deployment-files

echo.
echo ================================
echo 🚀 HRMS Application FTP Deployment
echo ================================
echo.

REM Check if Docker is available
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed or not available in PATH!
    pause
    exit /b 1
)

echo [INFO] Docker is available
echo.

REM Cleanup function
:cleanup
echo [INFO] Cleaning up resources...
docker rm -f %CONTAINER_NAME% >nul 2>&1
if exist "%DEPLOYMENT_DIR%" rmdir /s /q "%DEPLOYMENT_DIR%"
goto :eof

REM Main deployment process
echo [INFO] Building Docker image: %IMAGE_NAME%
docker build -t %IMAGE_NAME% .
if errorlevel 1 (
    echo [ERROR] Docker build failed!
    call :cleanup
    pause
    exit /b 1
)
echo [SUCCESS] Docker image built successfully
echo.

echo [INFO] Extracting application files from Docker image...
docker create --name %CONTAINER_NAME% %IMAGE_NAME%
if errorlevel 1 (
    echo [ERROR] Failed to create temporary container!
    call :cleanup
    pause
    exit /b 1
)

REM Create deployment directory
if exist "%DEPLOYMENT_DIR%" rmdir /s /q "%DEPLOYMENT_DIR%"
mkdir "%DEPLOYMENT_DIR%"

REM Copy files from container
docker cp %CONTAINER_NAME%:/app/. "%DEPLOYMENT_DIR%"
if errorlevel 1 (
    echo [ERROR] Failed to extract files from container!
    call :cleanup
    pause
    exit /b 1
)
echo [SUCCESS] Files extracted successfully
echo.

REM Count files
for /f %%i in ('dir /s /b "%DEPLOYMENT_DIR%\*" ^| find /c /v ""') do set file_count=%%i
echo [INFO] Total files extracted: !file_count!
echo.

REM Upload to FTP using PowerShell
echo [INFO] Uploading files to FTP server: %FTP_SERVER%
powershell -Command "& {
    $ftpServer = '%FTP_SERVER%'
    $ftpUsername = '%FTP_USERNAME%'
    $ftpPassword = '%FTP_PASSWORD%'
    $localDir = '%DEPLOYMENT_DIR%'
    
    Write-Host '[INFO] Connecting to FTP server: ' $ftpServer
    
    try {
        $files = Get-ChildItem -Path $localDir -Recurse -File
        $totalFiles = $files.Count
        $currentFile = 0
        $successCount = 0
        
        Write-Host '[INFO] Found' $totalFiles 'files to upload'
        
        foreach ($file in $files) {
            $currentFile++
            $relativePath = $file.FullName.Substring($localDir.Length).Replace('\', '/')
            if ($relativePath.StartsWith('/')) {
                $relativePath = $relativePath.Substring(1)
            }
            
            $ftpUri = 'ftp://' + $ftpServer + '/' + $relativePath
            Write-Progress -Activity 'Uploading files' -Status ('Uploading ' + $relativePath) -PercentComplete (($currentFile / $totalFiles) * 100)
            
            try {
                # Create directory if needed
                $remoteDir = Split-Path $ftpUri -Parent
                if ($remoteDir -ne ('ftp://' + $ftpServer)) {
                    try {
                        $dirRequest = [System.Net.FtpWebRequest]::Create($remoteDir)
                        $dirRequest.Method = [System.Net.WebRequestMethods+Ftp]::MakeDirectory
                        $dirRequest.Credentials = New-Object System.Net.NetworkCredential($ftpUsername, $ftpPassword)
                        $dirRequest.UsePassive = $true
                        $dirResponse = $dirRequest.GetResponse()
                        $dirResponse.Close()
                    } catch {
                        # Directory might already exist, ignore error
                    }
                }
                
                # Upload file
                $ftpRequest = [System.Net.FtpWebRequest]::Create($ftpUri)
                $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::UploadFile
                $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($ftpUsername, $ftpPassword)
                $ftpRequest.UseBinary = $true
                $ftpRequest.UsePassive = $true
                
                $fileContent = [System.IO.File]::ReadAllBytes($file.FullName)
                $ftpRequest.ContentLength = $fileContent.Length
                
                $requestStream = $ftpRequest.GetRequestStream()
                $requestStream.Write($fileContent, 0, $fileContent.Length)
                $requestStream.Close()
                
                $response = $ftpRequest.GetResponse()
                $response.Close()
                
                $successCount++
                
            } catch {
                Write-Host '[WARNING] Failed to upload:' $relativePath '- Error:' $_.Exception.Message -ForegroundColor Yellow
            }
        }
        
        Write-Progress -Activity 'Uploading files' -Completed
        
        if ($successCount -eq $totalFiles) {
            Write-Host '[SUCCESS] All files uploaded successfully!' -ForegroundColor Green
            exit 0
        } elseif ($successCount -gt 0) {
            Write-Host '[WARNING] Partial upload completed:' $successCount 'of' $totalFiles 'files uploaded' -ForegroundColor Yellow
            exit 0
        } else {
            Write-Host '[ERROR] No files were uploaded successfully!' -ForegroundColor Red
            exit 1
        }
        
    } catch {
        Write-Host '[ERROR] FTP upload failed:' $_.Exception.Message -ForegroundColor Red
        exit 1
    }
}"

if errorlevel 1 (
    echo [ERROR] FTP upload failed!
    call :cleanup
    pause
    exit /b 1
)

echo [SUCCESS] FTP upload completed successfully!
echo.

REM Deployment summary
echo ================================
echo 🎉 Deployment Summary
echo ================================
echo [SUCCESS] Application deployed to: http://%FTP_SERVER%
echo.
echo 📚 Available URLs:
echo    • Main Application: http://%FTP_SERVER%
echo    • API Documentation: http://%FTP_SERVER%/api/swagger
echo    • Health Check: http://%FTP_SERVER%/api/v1/health
echo.
echo 🔑 Default Login Credentials:
echo    • Super Admin: <EMAIL> / Admin123!
echo    • Org Admin: <EMAIL> / Admin123!
echo.
echo 📋 Next Steps:
echo    1. Ensure .NET 8 runtime is installed on the server
echo    2. Configure the web server to serve the application on port 80
echo    3. Start the application: dotnet HRMS.API.dll
echo    4. Ensure the server has permissions to bind to port 80 (may require admin)
echo.

REM Test connectivity
echo [INFO] Testing server connectivity...
curl -f -s --connect-timeout 10 "http://%FTP_SERVER%/api/v1/health" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] ⚠️  Server is not responding yet - This is normal if the server needs configuration
) else (
    echo [SUCCESS] ✅ Server is responding - Application is running!
)

echo.
echo [SUCCESS] Deployment process completed!

REM Cleanup
call :cleanup

echo.
pause
